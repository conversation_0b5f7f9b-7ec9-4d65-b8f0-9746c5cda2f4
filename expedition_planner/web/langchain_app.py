"""
Flask web application for LangChain-based expedition document processing.
"""

import json
import logging
import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# Import robust extraction module
try:
    from .robust_extraction import extract_structured_data_with_validation
    HAS_ROBUST_EXTRACTION = True
except ImportError:
    HAS_ROBUST_EXTRACTION = False

from flask import (
    Flask,
    flash,
    jsonify,
    redirect,
    render_template,
    request,
    send_file,
    send_from_directory,
    url_for,
)
from flask_socketio import SocketIO, emit, join_room, leave_room
from werkzeug.utils import secure_filename

from ..config import get_config
from ..config.langchain_config import get_langchain_config
from ..core.langchain_processor import LangChainExpeditionProcessor
from ..tools.template_generator import TemplateGeneratorTool
from ..core.text_extractor import TextExtractor
from ..utils.file_cleanup import FileCleanupManager
from ..utils.error_handlers import handle_expedition_errors, file_processing_error_handler
from ..utils.performance import parallel_processor, performance_monitor, memory_efficient

logger = logging.getLogger(__name__)

# Global variables
active_sessions = {}
langchain_processor = LangChainExpeditionProcessor()
socketio = None


@performance_monitor
@memory_efficient(max_memory_mb=1000.0)
def process_documents_directly(documents_dir, expedition_name, output_dir):
    """
    Enhanced direct processing with OCR validation.
    Extracts text using both standard methods and OCR, validates results,
    and generates JSON templates.
    """
    try:
        logger.info(f"Starting enhanced processing with OCR for {expedition_name}")

        # Initialize tools
        text_extractor = TextExtractor()
        template_generator = TemplateGeneratorTool()

        # Get all document files
        doc_files = []
        for ext in ['.pdf', '.docx', '.doc', '.txt', '.md', '.png', '.jpg', '.jpeg', '.tiff', '.bmp']:
            doc_files.extend(Path(documents_dir).glob(f"*{ext}"))

        if not doc_files:
            return {
                "success": False,
                "error": "No supported document files found",
                "status": "failed"
            }

        logger.info(f"Found {len(doc_files)} documents to process")

        # Process each document
        all_extracted_data = []
        extraction_stats = {
            "total_files": len(doc_files),
            "successful_extractions": 0,
            "ocr_used": 0,
            "standard_extraction_used": 0,
            "combined_results_used": 0,
            "failed_extractions": 0
        }
        
        for doc_file in doc_files:
            try:
                logger.info(f"Processing document: {doc_file.name}")
                
                # Extract text from document with enhanced OCR validation
                start_time = datetime.now()
                text_content = text_extractor.extract_text(str(doc_file))
                extraction_time = (datetime.now() - start_time).total_seconds()
                
                if not text_content or len(text_content.strip()) < 50:
                    logger.warning(f"Insufficient text extracted from {doc_file.name}")
                    extraction_stats["failed_extractions"] += 1
                    continue

                # Log extraction method used (based on file extension)
                file_extension = doc_file.suffix.lower()
                if file_extension in ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']:
                    extraction_stats["ocr_used"] += 1
                    logger.info(f"OCR extraction used for {doc_file.name}")
                elif file_extension == '.pdf' and text_extractor.ocr_enabled and text_extractor.validation_enabled:
                    # Safer approach to determine which method was used
                    try:
                        # Check if we can access the log buffer
                        if hasattr(logger, 'handlers') and logger.handlers and hasattr(logger.handlers[0], 'buffer'):
                            last_log = logger.handlers[0].formatter.format(logger.handlers[0].buffer[-1])
                            if "Combining results" in last_log:
                                extraction_stats["combined_results_used"] += 1
                                logger.info(f"Combined extraction (OCR + standard) used for {doc_file.name}")
                            elif "Using OCR result" in last_log:
                                extraction_stats["ocr_used"] += 1
                                logger.info(f"OCR extraction preferred for {doc_file.name}")
                            else:
                                extraction_stats["standard_extraction_used"] += 1
                                logger.info(f"Standard extraction preferred for {doc_file.name}")
                        else:
                            # Default to standard extraction if we can't determine
                            extraction_stats["standard_extraction_used"] += 1
                            logger.info(f"Standard extraction assumed for {doc_file.name} (log buffer not accessible)")
                    except (IndexError, AttributeError) as e:
                        # Handle any errors accessing the log buffer
                        extraction_stats["standard_extraction_used"] += 1
                        logger.info(f"Standard extraction assumed for {doc_file.name} (error: {e})")
                else:
                    extraction_stats["standard_extraction_used"] += 1
                    logger.info(f"Standard extraction used for {doc_file.name}")

                # Create structured data from text using robust extraction if available
                if HAS_ROBUST_EXTRACTION:
                    logger.info(f"Using robust extraction for {doc_file.name}")
                    structured_data = extract_structured_data_with_validation(text_content, doc_file.name)
                else:
                    logger.info(f"Using basic extraction for {doc_file.name}")
                    structured_data = extract_structured_data_from_text(text_content, doc_file.name)
                
                # Add extraction metadata
                extraction_metadata = {
                    "extraction_time_seconds": extraction_time,
                    "extraction_timestamp": datetime.now().isoformat(),
                    "characters_extracted": len(text_content),
                    "ocr_enabled": text_extractor.ocr_enabled,
                    "validation_enabled": text_extractor.validation_enabled,
                    "robust_extraction_used": HAS_ROBUST_EXTRACTION
                }
                
                # Add OCR metrics if available
                if hasattr(text_extractor, 'extraction_metrics'):
                    extraction_metadata["ocr_metrics"] = text_extractor.extraction_metrics
                
                # Add metadata to structured data
                if "_extraction_metadata" not in structured_data:
                    structured_data["_extraction_metadata"] = extraction_metadata
                else:
                    structured_data["_extraction_metadata"].update(extraction_metadata)
                
                all_extracted_data.append(structured_data)
                extraction_stats["successful_extractions"] += 1

            except Exception as e:
                logger.error(f"Error processing {doc_file.name}: {e}")
                extraction_stats["failed_extractions"] += 1
                continue

        if not all_extracted_data:
            return {
                "success": False,
                "error": "No data could be extracted from documents",
                "status": "failed",
                "extraction_stats": extraction_stats
            }

        # Generate JSON templates for each extracted data
        generated_files = []
        for i, data in enumerate(all_extracted_data):
            try:
                # Ensure all required fields are present and not None
                # Validate schedule data
                if "schedule" not in data or not data["schedule"]:
                    data["schedule"] = [
                        {"time": "08:00", "type": "arrival", "description": "Arrival at location"},
                        {"time": "12:00", "type": "departure", "description": "Departure from location"}
                    ]
                
                # Ensure schedule items have all required fields
                for item in data["schedule"]:
                    if not isinstance(item, dict):
                        continue
                    if "time" not in item or not item["time"]:
                        item["time"] = "12:00"
                    if "description" not in item or not item["description"]:
                        item["description"] = "Activity"
                    if "type" not in item:
                        item["type"] = "activity"
                
                # Generate JSON template
                json_data = json.dumps(data)
                location = data.get("location", "Unknown_Location")
                if not location:
                    location = "Unknown_Location"

                # Use template generator to create and save JSON file
                file_path = template_generator._run(
                    extracted_data=json_data,
                    operation_type="combined",
                    location=location,
                    output_path=output_dir
                )

                generated_files.append(file_path)
                logger.info(f"Generated JSON file: {file_path}")

            except Exception as e:
                logger.error(f"Error generating JSON template {i}: {e}")
                # Create a minimal valid template as fallback
                try:
                    minimal_data = {
                        "date": datetime.now().strftime("%Y-%m-%d"),
                        "location": "Unknown_Location",
                        "schedule": [
                            {"time": "08:00", "type": "arrival", "description": "Arrival at location"},
                            {"time": "12:00", "type": "departure", "description": "Departure from location"}
                        ],
                        "groups": []
                    }
                    json_data = json.dumps(minimal_data)
                    file_path = template_generator._run(
                        extracted_data=json_data,
                        operation_type="combined",
                        location="Unknown_Location",
                        output_path=output_dir
                    )
                    generated_files.append(file_path)
                    logger.info(f"Generated fallback JSON file: {file_path}")
                except Exception as fallback_error:
                    logger.error(f"Even fallback template generation failed: {fallback_error}")
                    continue

        return {
            "success": True,
            "status": "completed",
            "message": f"Successfully processed {len(doc_files)} documents and generated {len(generated_files)} JSON files",
            "generated_files": generated_files,
            "extraction_stats": extraction_stats,
            "steps": {
                "text_extraction": {
                    "success": True,
                    "files_processed": len(doc_files),
                    "successful_extractions": extraction_stats["successful_extractions"],
                    "ocr_used": extraction_stats["ocr_used"],
                    "standard_extraction_used": extraction_stats["standard_extraction_used"],
                    "combined_results_used": extraction_stats["combined_results_used"]
                },
                "json_generation": {
                    "success": True,
                    "files_generated": len(generated_files),
                    "file_paths": generated_files
                }
            }
        }

    except Exception as e:
        logger.error(f"Error in enhanced processing: {e}")
        return {
            "success": False,
            "error": str(e),
            "status": "failed"
        }


def extract_structured_data_from_text(text_content, filename):
    """
    Extract structured data from text content using robust parsing and validation.
    Uses pattern matching with validation to ensure high-quality extraction.
    """
    import re
    from datetime import datetime
    
    # Try to import robust extraction components
    try:
        from ..core.extraction_validator import ExtractionValidator, RobustDateParser
        has_validator = True
        validator = ExtractionValidator()
        date_parser = RobustDateParser()
        logger.info("Using robust extraction validator")
    except ImportError:
        has_validator = False
        validator = None
        date_parser = None
        logger.warning("Robust extraction validator not available, using basic extraction")

    # Initialize structure with empty/null values instead of defaults
    data = {
        "date": None,  # Don't default to current date
        "location": None,
        "arrival_time": None,
        "departure_time": None,
        "operation_type": "combined",
        "groups": [],
        "schedule": [],
        "tides": [],
        "equipment": {
            "zodiacs": None,
            "twins": None,
            "other": []
        },
        "personnel": {
            "total_count": 0,
            "guides": [],
            "drivers": []
        },
        "weather": None,
        "notes": f"Extracted from: {filename}"
    }

    for pattern in date_patterns:
        match = re.search(pattern, text_content, re.IGNORECASE)
        if match:
            try:
                # Try to parse the date
                date_str = match.group(0)
                # Simple date parsing - you could enhance this
                if '/' in date_str or '-' in date_str:
                    parts = re.split('[/-]', date_str)
                    if len(parts) == 3:
                        # Assume YYYY-MM-DD or MM/DD/YYYY format
                        if len(parts[0]) == 4:
                            data["date"] = f"{parts[0]}-{parts[1].zfill(2)}-{parts[2].zfill(2)}"
                        else:
                            data["date"] = f"{parts[2]}-{parts[0].zfill(2)}-{parts[1].zfill(2)}"
                break
            except:
                continue

    # Extract location patterns
    location_patterns = [
        r'Location:\s*([^\n\r]+)',
        r'([A-Z][a-z]+\s+[A-Z][a-z]+\s+(?:River|Island|Bay|Reef|Falls))',
        r'([A-Z][a-z]+\s+(?:River|Island|Bay|Reef|Falls))'
    ]

    for pattern in location_patterns:
        match = re.search(pattern, text_content, re.IGNORECASE)
        if match:
            location = match.group(1).strip()
            if len(location) > 3:  # Avoid single letters
                data["location"] = location
                break

    # Extract time patterns for schedule
    time_patterns = r'(\d{1,2}:\d{2})\s*[-–]\s*([^\n\r]+)'
    times = re.findall(time_patterns, text_content)

    for time_match in times[:10]:  # Limit to first 10 matches
        time_str, description = time_match
        data["schedule"].append({
            "time": time_str,
            "type": "activity",
            "description": description.strip()[:100],  # Limit description length
            "location": data["location"]
        })

    # Extract group information
    group_patterns = [
        r'(Yellow|Blue|Red|Green)\s+Group[:\s]*([^\n\r]+)',
        r'Group\s+(Yellow|Blue|Red|Green)[:\s]*([^\n\r]+)'
    ]

    colors = ["Yellow", "Blue", "Red", "Green"]
    for pattern in group_patterns:
        matches = re.findall(pattern, text_content, re.IGNORECASE)
        for match in matches:
            color = match[0].title()
            if color in colors:
                data["groups"].append({
                    "groupName": f"{color} Group",
                    "color": color,
                    "departureTime": "08:00",
                    "returnTime": "10:00",
                    "activity": "Zodiac Cruise"
                })

    # If no groups found, add default groups
    if not data["groups"]:
        for color in colors:
            data["groups"].append({
                "groupName": f"{color} Group",
                "color": color,
                "departureTime": "08:00",
                "returnTime": "10:00",
                "activity": "Zodiac Cruise"
            })

    # Extract equipment numbers
    zodiac_match = re.search(r'(\d+)\s*(?:mach\s*5s?|zodiacs?)', text_content, re.IGNORECASE)
    if zodiac_match:
        data["equipment"]["zodiacs"] = int(zodiac_match.group(1))

    twin_match = re.search(r'(\d+)\s*twins?', text_content, re.IGNORECASE)
    if twin_match:
        data["equipment"]["twins"] = int(twin_match.group(1))

    return data


def clean_result_for_json(result):
    """Clean result object to make it JSON serializable."""
    if isinstance(result, dict):
        cleaned = {}
        for key, value in result.items():
            if key in ["intermediate_steps", "agent_scratchpad"]:
                # Skip non-serializable agent data
                continue
            elif isinstance(value, (dict, list)):
                cleaned[key] = clean_result_for_json(value)
            elif hasattr(value, "__dict__"):
                # Convert objects to string representation
                cleaned[key] = str(value)
            else:
                cleaned[key] = value
        return cleaned
    elif isinstance(result, list):
        return [clean_result_for_json(item) for item in result]
    elif hasattr(result, "__dict__"):
        return str(result)
    else:
        return result


def create_langchain_app(config_override: Optional[Dict[str, Any]] = None) -> Flask:  # noqa: C901
    """Create and configure the Flask application with LangChain support."""
    app = Flask(__name__, static_folder='static')
    global socketio

    # Load configuration
    config = get_config()

    if config_override:
        config.update(config_override)

    app.config.update(config)
    app.secret_key = config.get("secret_key", "dev-key-change-in-production")

    # Initialize SocketIO for real-time agent updates with proper configuration
    socketio = SocketIO(
        app,
        cors_allowed_origins="*",
        serve_client=False,  # Disable built-in client to avoid version conflicts
        engineio_logger=False,  # Reduce verbose logging
        socketio_logger=False,  # Reduce verbose logging
        async_mode='threading',  # Use threading mode for better compatibility
        ping_timeout=60,  # Increase ping timeout
        ping_interval=25,  # Set ping interval
        logger=False,  # Disable additional logging
        always_connect=True  # Allow connections even if client version differs
    )

    # Ensure directories exist
    upload_dir = Path(config["directories"]["uploads"])
    output_dir = Path(config["directories"]["outputs"])
    upload_dir.mkdir(exist_ok=True)
    output_dir.mkdir(exist_ok=True)

    # Initialize file cleanup manager
    cleanup_manager = FileCleanupManager(str(upload_dir), str(output_dir))

    # Run initial cleanup on startup
    try:
        cleanup_stats = cleanup_manager.cleanup_old_uploads(days_old=7)
        cleanup_manager.enforce_size_limits(max_size_mb=100)
        logger.info(f"Startup cleanup completed: {cleanup_stats}")
    except Exception as e:
        logger.warning(f"Startup cleanup failed: {e}")

    # Routes
    @app.route("/favicon.ico")
    def favicon():
        """Serve the favicon."""
        return send_from_directory(
            os.path.join(app.root_path, "static"),
            "favicon.ico",
            mimetype="image/vnd.microsoft.icon",
        )
        
    @app.route("/apple-touch-icon.png")
    @app.route("/apple-touch-icon-precomposed.png")
    def apple_touch_icon():
        """Serve the Apple touch icon."""
        return send_from_directory(
            os.path.join(app.root_path, "static"),
            "apple-touch-icon.png",
            mimetype="image/png",
        )
        
    @app.route("/")
    def index():
        """Main page with expedition report converter."""
        return render_template("langchain_index.html")

    @app.route("/agent-interface")
    def agent_interface():
        """Document conversion page."""
        return render_template("agent_interface.html")

    @app.route("/pattern-analysis")
    def pattern_analysis():
        """Pattern analysis page."""
        return render_template("pattern_analysis.html")

    @app.route("/api/upload-documents", methods=["POST"])
    def upload_documents():
        """Handle document upload for LangChain processing."""
        try:
            if "files" not in request.files:
                return jsonify({"success": False, "error": "No files provided"})

            files = request.files.getlist("files")
            if not files or all(f.filename == "" for f in files):
                return jsonify({"success": False, "error": "No files selected"})

            # Create session
            session_id = str(uuid.uuid4())
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_dir = upload_dir / f"{session_id}_{timestamp}"
            session_dir.mkdir(exist_ok=True)

            uploaded_files = []

            for file in files:
                if file and file.filename:
                    filename = secure_filename(file.filename)
                    file_path = session_dir / filename
                    file.save(str(file_path))

                    uploaded_files.append(
                        {
                            "name": filename,
                            "path": str(file_path),
                            "size": file_path.stat().st_size,
                        }
                    )

            # Store session info
            active_sessions[session_id] = {
                "session_id": session_id,
                "upload_time": datetime.now(),
                "files": uploaded_files,
                "session_dir": str(session_dir),
                "status": "uploaded",
            }

            # Trigger cleanup after upload to maintain storage limits
            try:
                cleanup_manager.enforce_size_limits(max_size_mb=100)
                cleanup_manager.cleanup_empty_directories()
            except Exception as e:
                logger.warning(f"Post-upload cleanup failed: {e}")

            logger.info(
                f"Uploaded {len(uploaded_files)} files for session {session_id}"
            )

            return jsonify(
                {
                    "success": True,
                    "session_id": session_id,
                    "files": uploaded_files,
                    "message": f"Successfully uploaded {len(uploaded_files)} files",
                }
            )

        except Exception as e:
            logger.error(f"Error uploading documents: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/process-expedition", methods=["POST"])
    def process_expedition():
        """Start LangChain-based expedition processing with focus on JSON generation."""
        try:
            data = request.get_json()
            session_id = data.get("session_id")
            expedition_name = data.get("expedition_name", "Unnamed Expedition")
            # Default to False - focus on JSON generation first
            enable_analysis = data.get("enable_analysis", False)

            if session_id not in active_sessions:
                return jsonify({"success": False, "error": "Invalid session ID"})

            session_info = active_sessions[session_id]
            session_dir = session_info["session_dir"]

            # Create output directory
            output_dir_path = output_dir / f"expedition_{session_id}"
            output_dir_path.mkdir(exist_ok=True)

            # Start processing in background
            def process_in_background():
                try:
                    # Additional logging and status emit before processing
                    logger.info("🟢 Launching background processing with simplified pipeline")
                    socketio.emit("agent_status", {
                        "agent": "controller",
                        "status": "started",
                        "message": "Pipeline has been launched"
                    }, room=session_id)

                    # Emit status updates via WebSocket - now with OCR step
                    socketio.emit(
                        "agent_status",
                        {"agent": "text_extractor", "status": "processing", "message": "Starting document processing with OCR and text extraction"},
                        room=session_id,
                    )
                    
                    # Emit OCR status
                    socketio.emit(
                        "agent_status",
                        {"agent": "ocr_processor", "status": "processing", "message": "Performing OCR on documents and validating results"},
                        room=session_id,
                    )

                    # Use enhanced processing with OCR validation
                    result = process_documents_directly(
                        session_dir,
                        expedition_name,
                        str(output_dir_path)
                    )
                    
                    # Update OCR status based on result
                    if result.get("success", False):
                        extraction_stats = result.get("extraction_stats", {})
                        ocr_count = extraction_stats.get("ocr_used", 0) + extraction_stats.get("combined_results_used", 0)
                        
                        socketio.emit(
                            "agent_status",
                            {
                                "agent": "ocr_processor", 
                                "status": "completed", 
                                "message": f"OCR processing complete: {ocr_count} documents used OCR"
                            },
                            room=session_id,
                        )
                        
                        # Emit JSON generation status
                        socketio.emit(
                            "agent_status",
                            {"agent": "json_generator", "status": "processing", "message": "Generating JSON templates from extracted data"},
                            room=session_id,
                        )
                    else:
                        socketio.emit(
                            "agent_status",
                            {"agent": "ocr_processor", "status": "failed", "message": "OCR processing encountered errors"},
                            room=session_id,
                        )

                    logger.info("✅ Background processing complete")

                    # Clean result for JSON serialization
                    cleaned_result = clean_result_for_json(result)

                    # Update session with results
                    active_sessions[session_id]["result"] = cleaned_result
                    active_sessions[session_id]["status"] = (
                        "completed" if result.get("success", False) else "failed"
                    )

                    # Extract JSON files from the result
                    json_files = {}

                    # Log the structure of the result for debugging
                    logger.info(f"Result structure: {list(result.keys()) if isinstance(result, dict) else type(result)}")

                    # Handle direct processing result structure
                    if isinstance(result, dict) and "generated_files" in result:
                        # Direct processing result
                        for file_path in result.get("generated_files", []):
                            file_name = Path(file_path).stem
                            json_files[file_name] = Path(file_path).as_posix()
                        logger.info(f"Found {len(json_files)} JSON files from direct processing")

                    # Also check for LangChain workflow structure (for backward compatibility)
                    elif isinstance(result, dict) and "steps" in result:
                        logger.info(f"Steps structure: {list(result['steps'].keys())}")
                        json_gen_results = result["steps"].get("json_generation", {})
                        if json_gen_results:
                            logger.info(f"JSON generation structure: {json_gen_results}")
                        # Process each location's JSON generation results
                        for location, gen_result in json_gen_results.items():
                            logger.info(f"Processing location: {location}, result: {gen_result}")
                            if gen_result.get("success") and "generated_files" in gen_result:
                                json_files[location] = gen_result["generated_files"]
                            # Also check for file_path which might be used instead of generated_files
                            elif gen_result.get("success") and "file_path" in gen_result:
                                # Create a dictionary with operation_type as key and file_path as value
                                operation_type = gen_result.get("operation_type", "combined")
                                json_files[location] = {operation_type: gen_result["file_path"]}

                    # If no JSON files found in the steps, look for them directly in the result
                    if not json_files and "generated_files" in result:
                        # If there's a direct generated_files field, use it
                        for location, files in result["generated_files"].items():
                            json_files[location] = files

                    # If still no JSON files, search for any .json files in the output directory
                    if not json_files:
                        # Get the output directory from the config
                        output_directory = Path(config["directories"]["outputs"])
                        expedition_output_dir = output_directory / f"expedition_{session_id}"

                        if expedition_output_dir.exists():
                            logger.info(f"Searching for JSON files in: {expedition_output_dir}")
                            json_file_paths = list(expedition_output_dir.glob("*.json"))
                            logger.info(f"Found JSON files: {json_file_paths}")

                            # Group files by location (extracted from filename)
                            for json_path in json_file_paths:
                                if "workflow_state" not in json_path.name:  # Skip workflow state files
                                    # Use the full filename as the key for simplicity
                                    operation_name = json_path.stem  # Use the filename without extension

                                    # Create a flat mapping structure that's easier for the frontend to handle
                                    json_files[operation_name] = json_path.as_posix()

                    # Emit completion with phase information and JSON files
                    completion_message = {
                        **cleaned_result,
                        "phase": "json_generation",
                        "next_step": "pattern_analysis" if not enable_analysis else "complete",
                        "json_files": json_files  # Add JSON files to the response
                    }

                    # Log the JSON files being sent to the client
                    logger.info(f"Sending JSON files to client: {json_files}")

                    socketio.emit(
                        "processing_complete", completion_message, room=session_id
                    )

                except Exception as e:
                    logger.error(f"Background processing error: {e}")
                    socketio.emit("error", {"message": str(e)}, room=session_id)

            # Start background processing
            socketio.start_background_task(process_in_background)

            processing_type = "JSON Generation" if not enable_analysis else "Full Processing (JSON + Analysis)"

            return jsonify(
                {
                    "success": True,
                    "session_id": session_id,
                    "message": f"{processing_type} started",
                    "processing_type": "json_generation" if not enable_analysis else "full_processing"
                }
            )

        except Exception as e:
            logger.error(f"Error starting expedition processing: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/session-status/<session_id>")
    def get_session_status(session_id):
        """Get status of a processing session."""
        try:
            if session_id in active_sessions:
                session = active_sessions[session_id]
                return jsonify({"success": True, "session": session})
            else:
                return jsonify({"success": False, "error": "Session not found"})

        except Exception as e:
            logger.error(f"Error getting session status: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/analyze-json-files", methods=["POST"])
    def analyze_json_files():
        """Analyze existing JSON files for patterns using the enhanced batch analysis method."""
        try:
            data = request.get_json()
            json_file_paths = data.get("json_files", [])

            if not json_file_paths:
                return jsonify({"success": False, "error": "No JSON files provided"})

            if len(json_file_paths) < 2:
                return jsonify({
                    "success": False,
                    "error": "Pattern analysis requires at least 2 JSON files for meaningful comparison",
                    "recommendation": "Select multiple expedition operation files to discover patterns"
                })

            # Use the enhanced batch analysis method
            analysis_result = langchain_processor.analyze_multiple_json_files(json_file_paths)

            return jsonify({
                "success": analysis_result.get("success", False),
                **clean_result_for_json(analysis_result)
            })

        except Exception as e:
            logger.error(f"Error analyzing JSON files: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/list-json-files")
    def list_json_files():
        """List available JSON files for analysis."""
        try:
            json_files = []

            # Scan consolidated_outputs directory for JSON files
            consolidated_dir = Path("consolidated_outputs")
            if consolidated_dir.exists():
                for json_file in consolidated_dir.glob("*.json"):
                    # Skip workflow state files
                    if "workflow_state" not in json_file.name:
                        file_info = {
                            "path": str(json_file),
                            "name": json_file.name,
                            "size": json_file.stat().st_size,
                            "modified": datetime.fromtimestamp(
                                json_file.stat().st_mtime
                            ).isoformat(),
                        }
                        json_files.append(file_info)

            # Also scan output directories for JSON files (for backward compatibility)
            if output_dir.exists():
                for expedition_dir in output_dir.iterdir():
                    if expedition_dir.is_dir():
                        for json_file in expedition_dir.glob("*.json"):
                            # Skip workflow state files
                            if "workflow_state" not in json_file.name:
                                file_info = {
                                    "path": str(json_file),
                                    "name": json_file.name,
                                    "expedition": expedition_dir.name,
                                    "size": json_file.stat().st_size,
                                    "modified": datetime.fromtimestamp(
                                        json_file.stat().st_mtime
                                    ).isoformat(),
                                }
                                json_files.append(file_info)

            # Sort by modification time (newest first)
            json_files.sort(key=lambda x: x["modified"], reverse=True)

            return jsonify(
                {
                    "success": True,
                    "json_files": json_files,
                    "total_files": len(json_files),
                }
            )

        except Exception as e:
            logger.error(f"Error listing JSON files: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/processor-capabilities")
    def get_processor_capabilities():
        """Get LangChain processor capabilities."""
        try:
            capabilities = langchain_processor.get_processor_capabilities()
            return jsonify({"success": True, "capabilities": capabilities})

        except Exception as e:
            logger.error(f"Error getting capabilities: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/validate-configuration")
    def validate_configuration():
        """Validate LangChain processor configuration."""
        try:
            # Validate both processor and centralized config
            processor_validation = langchain_processor.validate_configuration()

            try:
                from ..config.config_manager import validate_config
                config_validation = validate_config()
            except ImportError:
                config_validation = {"valid": True, "errors": [], "warnings": ["Config manager not available"]}

            # Combine validations
            combined_validation = {
                "processor": processor_validation,
                "configuration": config_validation,
                "overall_valid": processor_validation.get("valid", False) and config_validation.get("valid", False)
            }

            return jsonify({"success": True, "validation": combined_validation})

        except Exception as e:
            logger.error(f"Error validating configuration: {e}")
            return jsonify({"success": False, "error": str(e)})

    @app.route("/api/download/<path:filename>")
    def download_file(filename):
        """Download generated files with secure path handling."""
        try:
            # Decode URL-encoded paths
            import urllib.parse
            decoded_filename = urllib.parse.unquote(filename)

            # Handle both absolute and relative paths securely
            file_path = None

            # If it's an absolute path, use it directly (but validate it's in allowed directories)
            if decoded_filename.startswith('/'):
                potential_path = Path(decoded_filename)
                # Security check: ensure the file is in allowed directories
                allowed_dirs = [
                    Path(config["directories"]["outputs"]),
                    Path(config["directories"]["uploads"])
                ]

                for allowed_dir in allowed_dirs:
                    try:
                        # Check if the file is within an allowed directory
                        potential_path.resolve().relative_to(allowed_dir.resolve())
                        file_path = potential_path
                        break
                    except ValueError:
                        continue
            else:
                # For relative paths, try different base directories
                base_dirs = [
                    Path(config["directories"]["outputs"]),
                    Path(config["directories"]["uploads"]),
                    Path.cwd(),  # Current working directory
                ]

                for base_dir in base_dirs:
                    potential_path = base_dir / decoded_filename
                    if potential_path.exists() and potential_path.is_file():
                        file_path = potential_path
                        break

            if file_path and file_path.exists() and file_path.is_file():
                # Check if the request wants JSON content or file download
                if request.args.get('format') == 'json' or request.headers.get('Accept') == 'application/json':
                    # Return JSON content
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    return jsonify(json.loads(content))
                else:
                    # Return file for download
                    return send_file(str(file_path), as_attachment=True, download_name=file_path.name)
            else:
                logger.warning(f"File not found for download: {decoded_filename}")
                return jsonify({"error": "File not found", "requested_path": decoded_filename}), 404

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in file {filename}: {e}")
            return jsonify({"error": "Invalid JSON file"}), 400
        except Exception as e:
            logger.error(f"Error downloading file {filename}: {e}")
            return jsonify({"error": str(e)}), 500
            
    @app.route("/api/view-json/<path:filename>")
    def view_json_file(filename):
        """View JSON file content with secure path handling."""
        try:
            # Decode URL-encoded paths
            import urllib.parse
            decoded_filename = urllib.parse.unquote(filename)

            # Handle both absolute and relative paths securely (same logic as download)
            file_path = None

            if decoded_filename.startswith('/'):
                potential_path = Path(decoded_filename)
                # Security check: ensure the file is in allowed directories
                allowed_dirs = [
                    Path(config["directories"]["outputs"]),
                    Path(config["directories"]["uploads"])
                ]

                for allowed_dir in allowed_dirs:
                    try:
                        potential_path.resolve().relative_to(allowed_dir.resolve())
                        file_path = potential_path
                        break
                    except ValueError:
                        continue
            else:
                # For relative paths, try different base directories
                base_dirs = [
                    Path(config["directories"]["outputs"]),
                    Path(config["directories"]["uploads"]),
                    Path.cwd(),
                ]

                for base_dir in base_dirs:
                    potential_path = base_dir / decoded_filename
                    if potential_path.exists() and potential_path.is_file():
                        file_path = potential_path
                        break

            if file_path and file_path.exists() and file_path.is_file():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return jsonify(json.loads(content))
            else:
                logger.warning(f"JSON file not found for viewing: {decoded_filename}")
                return jsonify({"error": "File not found", "requested_path": decoded_filename}), 404

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in file {filename}: {e}")
            return jsonify({"error": "Invalid JSON file"}), 400
        except Exception as e:
            logger.error(f"Error viewing JSON file {filename}: {e}")
            return jsonify({"error": str(e)}), 500
            
    @app.route("/api/search-json-files", methods=["POST"])
    def search_json_files():
        """Search for JSON files in the output directory."""
        try:
            data = request.get_json()
            output_directory = data.get("output_directory")
            session_id = data.get("session_id")
            
            # If session_id is provided, use it to find the output directory
            if session_id and session_id in active_sessions:
                # Get the output directory from the config
                output_directory = str(Path(config["directories"]["outputs"]) / f"expedition_{session_id}")
                logger.info(f"Using session output directory: {output_directory}")
            
            if not output_directory:
                # Use the default output directory from config
                output_directory = str(Path(config["directories"]["outputs"]))
                logger.info(f"Using default output directory: {output_directory}")
                
            output_path = Path(output_directory)
            if not output_path.exists() or not output_path.is_dir():
                return jsonify({"success": False, "error": "Output directory not found"}), 404
                
            # Search for JSON files
            json_files = {}
            json_file_paths = list(output_path.glob("*.json"))
            
            logger.info(f"Found {len(json_file_paths)} JSON files in {output_directory}")
            
            # Create a flat mapping structure that's easier for the frontend to handle
            for json_path in json_file_paths:
                if "workflow_state" not in json_path.name:  # Skip workflow state files
                    # Use the full filename as the key for simplicity
                    operation_name = json_path.stem  # Use the filename without extension
                    json_files[operation_name] = str(json_path)
            
            # If no JSON files found, try searching in subdirectories
            if not json_files:
                for subdir in output_path.iterdir():
                    if subdir.is_dir():
                        logger.info(f"Searching in subdirectory: {subdir}")
                        for json_path in subdir.glob("*.json"):
                            if "workflow_state" not in json_path.name:  # Skip workflow state files
                                # Use the full filename as the key for simplicity
                                operation_name = json_path.stem  # Use the filename without extension
                                json_files[operation_name] = str(json_path)
            
            return jsonify({
                "success": True,
                "json_files": json_files,
                "file_count": len(json_file_paths),
                "output_directory": str(output_path)
            })
            
        except Exception as e:
            logger.error(f"Error searching for JSON files: {e}")
            return jsonify({"success": False, "error": str(e)}), 500
            
    @app.route("/api/get-latest-json", methods=["GET"])
    def get_latest_json():
        """Get the latest generated JSON file from consolidated_outputs directory."""
        try:
            json_files = []

            # Check consolidated_outputs directory first (primary location)
            consolidated_dir = Path("consolidated_outputs")
            if consolidated_dir.exists():
                for json_file in consolidated_dir.glob("*.json"):
                    # Skip workflow state files
                    if "workflow_state" not in json_file.name:
                        json_files.append(json_file)

            # Also check the output directory for backward compatibility
            output_directory = Path(config["directories"]["outputs"])
            if output_directory.exists():
                expedition_dirs = [d for d in output_directory.iterdir() if d.is_dir() and d.name.startswith("expedition_")]
                for expedition_dir in expedition_dirs:
                    for json_file in expedition_dir.glob("*.json"):
                        if "workflow_state" not in json_file.name:
                            json_files.append(json_file)

            if not json_files:
                return jsonify({"success": False, "error": "No JSON files found"}), 404

            # Get the latest JSON file by modification time
            latest_json = max(json_files, key=lambda f: f.stat().st_mtime)
            logger.info(f"Latest JSON file: {latest_json}")

            # Read the JSON file
            with open(latest_json, 'r') as f:
                content = f.read()

            # Parse the JSON content
            json_data = json.loads(content)

            return jsonify({
                "success": True,
                "json_data": json_data,
                "file_path": str(latest_json),
                "file_name": latest_json.name,
                "modified": datetime.fromtimestamp(latest_json.stat().st_mtime).isoformat()
            })

        except Exception as e:
            logger.error(f"Error getting latest JSON file: {e}")
            return jsonify({"success": False, "error": str(e)}), 500

    @app.route("/api/cleanup-files", methods=["POST"])
    def cleanup_files():
        """Manual file cleanup endpoint."""
        try:
            data = request.get_json() or {}
            days_old = data.get("days_old", 7)
            max_size_mb = data.get("max_size_mb", 100)

            # Run cleanup operations
            cleanup_stats = cleanup_manager.cleanup_old_uploads(days_old)
            cleanup_manager.cleanup_duplicate_files()
            cleanup_manager.cleanup_empty_directories()
            cleanup_manager.enforce_size_limits(max_size_mb)

            return jsonify({
                "success": True,
                "cleanup_stats": cleanup_stats,
                "message": cleanup_manager.get_cleanup_report()
            })

        except Exception as e:
            logger.error(f"Manual cleanup failed: {e}")
            return jsonify({"success": False, "error": str(e)}), 500

    @app.route("/api/storage-status")
    def get_storage_status():
        """Get current storage status."""
        try:
            import os

            # Calculate directory sizes
            upload_size = sum(
                os.path.getsize(os.path.join(dirpath, filename))
                for dirpath, dirnames, filenames in os.walk(upload_dir)
                for filename in filenames
            ) / (1024 * 1024)  # Convert to MB

            output_size = sum(
                os.path.getsize(os.path.join(dirpath, filename))
                for dirpath, dirnames, filenames in os.walk(output_dir)
                for filename in filenames
            ) / (1024 * 1024)  # Convert to MB

            # Count files
            upload_files = sum(
                len(filenames)
                for dirpath, dirnames, filenames in os.walk(upload_dir)
            )

            output_files = sum(
                len(filenames)
                for dirpath, dirnames, filenames in os.walk(output_dir)
            )

            return jsonify({
                "success": True,
                "storage": {
                    "uploads": {
                        "size_mb": round(upload_size, 2),
                        "file_count": upload_files
                    },
                    "outputs": {
                        "size_mb": round(output_size, 2),
                        "file_count": output_files
                    },
                    "total_size_mb": round(upload_size + output_size, 2),
                    "total_files": upload_files + output_files
                }
            })

        except Exception as e:
            logger.error(f"Storage status check failed: {e}")
            return jsonify({"success": False, "error": str(e)}), 500

    @app.route("/api/performance-stats")
    def get_performance_stats():
        """Get current performance statistics."""
        try:
            from ..utils.performance import get_performance_stats
            from ..utils.circuit_breaker import get_circuit_breaker_status

            stats = get_performance_stats()
            circuit_status = get_circuit_breaker_status()

            return jsonify({
                "success": True,
                "performance": stats,
                "circuit_breakers": circuit_status,
                "active_sessions": len(active_sessions)
            })

        except Exception as e:
            logger.error(f"Performance stats check failed: {e}")
            return jsonify({"success": False, "error": str(e)}), 500



    # WebSocket events with enhanced error handling
    @socketio.on("connect")
    def handle_connect():
        """Handle WebSocket connection."""
        try:
            logger.info(f"Client connected: {request.sid}")
            emit("connected", {
                "message": "Connected to LangChain agent interface",
                "timestamp": datetime.now().isoformat(),
                "client_id": request.sid
            })
        except Exception as e:
            logger.error(f"Error in WebSocket connect: {e}")
            emit("error", {"message": "Connection error", "error": str(e)})

    @socketio.on("disconnect")
    def handle_disconnect():
        """Handle WebSocket disconnection."""
        try:
            logger.info(f"Client disconnected: {request.sid}")
            # Clean up any session associations for this client
            for session_id, session_info in active_sessions.items():
                if hasattr(session_info, 'client_id') and session_info.get('client_id') == request.sid:
                    session_info['client_disconnected'] = True
        except Exception as e:
            logger.error(f"Error in WebSocket disconnect: {e}")

    @socketio.on("join_session")
    def handle_join_session(data):
        """Join a processing session room with validation."""
        try:
            session_id = data.get("session_id")
            if not session_id:
                emit("error", {"message": "Session ID is required"})
                return

            if session_id not in active_sessions:
                emit("error", {"message": f"Session {session_id} not found"})
                return

            join_room(session_id)

            # Associate client with session
            active_sessions[session_id]['client_id'] = request.sid

            emit("joined_session", {
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "status": active_sessions[session_id].get("status", "unknown")
            })

            logger.info(f"Client {request.sid} joined session {session_id}")

        except Exception as e:
            logger.error(f"Error joining session: {e}")
            emit("error", {"message": "Failed to join session", "error": str(e)})

    @socketio.on("leave_session")
    def handle_leave_session(data):
        """Leave a processing session room."""
        try:
            session_id = data.get("session_id")
            if not session_id:
                emit("error", {"message": "Session ID is required"})
                return

            leave_room(session_id)

            # Remove client association
            if session_id in active_sessions:
                active_sessions[session_id].pop('client_id', None)

            emit("left_session", {
                "session_id": session_id,
                "timestamp": datetime.now().isoformat()
            })

            logger.info(f"Client {request.sid} left session {session_id}")

        except Exception as e:
            logger.error(f"Error leaving session: {e}")
            emit("error", {"message": "Failed to leave session", "error": str(e)})

    @socketio.on("ping")
    def handle_ping(data):
        """Handle ping for connection health check."""
        try:
            emit("pong", {
                "timestamp": datetime.now().isoformat(),
                "server_status": "healthy"
            })
        except Exception as e:
            logger.error(f"Error handling ping: {e}")

    @socketio.on("get_session_status")
    def handle_get_session_status(data):
        """Get current status of a session."""
        try:
            session_id = data.get("session_id")
            if not session_id:
                emit("error", {"message": "Session ID is required"})
                return

            if session_id in active_sessions:
                session_info = active_sessions[session_id]
                emit("session_status", {
                    "session_id": session_id,
                    "status": session_info.get("status", "unknown"),
                    "upload_time": session_info.get("upload_time", "").isoformat() if session_info.get("upload_time") else None,
                    "file_count": len(session_info.get("files", [])),
                    "timestamp": datetime.now().isoformat()
                })
            else:
                emit("error", {"message": f"Session {session_id} not found"})

        except Exception as e:
            logger.error(f"Error getting session status: {e}")
            emit("error", {"message": "Failed to get session status", "error": str(e)})

    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return render_template("error.html", error="Page not found"), 404

    @app.errorhandler(500)
    def internal_error(error):
        return render_template("error.html", error="Internal server error"), 500

    return app


def run_langchain_app(host="0.0.0.0", port=8080, debug=False):
    """Run the LangChain Flask application."""
    app = create_langchain_app()

    logger.info("Starting LangChain expedition processor web interface")
    logger.info(f"Server: http://{host}:{port}")

    socketio.run(app, host=host, port=port, debug=debug, allow_unsafe_werkzeug=True)


if __name__ == "__main__":
    run_langchain_app(debug=True)
