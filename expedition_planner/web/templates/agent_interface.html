<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Convert Expedition Reports - Expedition Report Converter</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/dist/agent.2ed995009a3671a643ae.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 3rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover, .upload-area.drag-over {
            border-color: #1a3a8f;
            background-color: #f8f9ff;
        }
        .processor-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .processor-status.ready { background: #d1ecf1; color: #0c5460; }
        .processor-status.processing { background: #fff3cd; color: #856404; }
        .processor-status.completed { background: #d4edda; color: #155724; }
        .processor-status.error { background: #f8d7da; color: #721c24; }
        
        .progress-container {
            display: none;
        }
        
        .log-entry {
            padding: 0.5rem;
            border-bottom: 1px solid #eee;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        .log-entry.log-info { color: #0c5460; }
        .log-entry.log-success { color: #155724; }
        .log-entry.log-error { color: #721c24; }
        .log-entry.log-warning { color: #856404; }
        
        .log-timestamp {
            color: #6c757d;
            margin-right: 1rem;
        }
        
        .processing-logs {
            height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        
        .file-item {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 0.75rem;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin-bottom: 0.5rem;
        }
        
        .file-info {
            flex-grow: 1;
        }
        
        .file-name {
            font-weight: 500;
            display: block;
        }
        
        .file-size {
            color: #6c757d;
            font-size: 0.875rem;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-badge.ready { background: #d1ecf1; color: #0c5460; }
        .status-badge.processing { background: #fff3cd; color: #856404; }
        .status-badge.completed { background: #d4edda; color: #155724; }
        
        .results-container {
            display: none;
        }
        
        .json-preview {
            background: #272822;
            color: #f8f8f2;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .json-key { color: #f92672; }
        .json-string { color: #a6e22e; }
        .json-number { color: #ae81ff; }
        .json-boolean { color: #66d9ef; }
        
        .file-link {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: #1a3a8f;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 0.25rem;
            transition: background 0.3s ease;
        }
        
        .file-link:hover {
            background: #0f2557;
            color: white;
        }
        
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
        }
        
        .step {
            flex: 1;
            text-align: center;
            position: relative;
        }
        
        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-weight: bold;
            position: relative;
            z-index: 2;
        }
        
        .step.active .step-circle {
            background-color: #1a3a8f;
            color: white;
        }
        
        .step.completed .step-circle {
            background-color: #28a745;
            color: white;
        }
        
        .step-title {
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        .step.active .step-title {
            color: #1a3a8f;
            font-weight: bold;
        }
        
        .step.completed .step-title {
            color: #28a745;
        }
        
        .step-connector {
            position: absolute;
            top: 20px;
            height: 2px;
            background-color: #e9ecef;
            width: 100%;
            left: 50%;
            z-index: 1;
        }
        
        .step.completed .step-connector {
            background-color: #28a745;
        }
        
        .file-group {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .file-group-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-file-export me-2"></i>
                Expedition Report Converter
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="/pattern-analysis">Analyze Patterns</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1 class="mb-4">
            <i class="fas fa-file-export me-2"></i>
            Convert Expedition Reports to JSON
        </h1>
        
        <!-- Step Indicator -->
        <div class="step-indicator mb-4">
            <div class="step active">
                <div class="step-circle">1</div>
                <div class="step-title">Upload Reports</div>
                <div class="step-connector"></div>
            </div>
            <div class="step">
                <div class="step-circle">2</div>
                <div class="step-title">Process Documents</div>
                <div class="step-connector"></div>
            </div>
            <div class="step">
                <div class="step-circle">3</div>
                <div class="step-title">Review JSON</div>
                <div class="step-connector"></div>
            </div>
            <div class="step">
                <div class="step-circle">4</div>
                <div class="step-title">Export Results</div>
            </div>
        </div>
        
        <div class="row">
            <!-- Upload Section -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i>
                            Upload Expedition Reports
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="expedition-name" class="form-label">Expedition Name</label>
                            <input type="text" class="form-control" id="expedition-name"
                                   placeholder="Enter expedition name (e.g., Montgomery Reef, King George River)">
                        </div>

                        <div class="mb-3">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>Processing Recommendation</h6>
                                <p class="mb-2">
                                    For best results, first convert your reports to JSON, then analyze patterns across multiple operations.
                                </p>
                            </div>

                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable-analysis">
                                <label class="form-check-label" for="enable-analysis">
                                    <i class="fas fa-chart-line me-2"></i>
                                    <strong>Include Pattern Analysis</strong>
                                </label>
                                <div class="form-text">
                                    <small class="text-muted">
                                        <strong>Unchecked (Recommended):</strong> Generate JSON templates only → Analyze patterns later with multiple operations<br>
                                        <strong>Checked:</strong> Generate JSON + attempt pattern analysis (limited effectiveness with single operation)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div id="upload-area" class="upload-area">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>Drop expedition reports here or click to browse</h5>
                            <p class="text-muted">Supported formats: PDF, DOCX, DOC, TXT, MD</p>
                            <input type="file" id="file-input" multiple accept=".pdf,.docx,.doc,.txt,.md" style="display: none;">
                        </div>
                        
                        <div id="file-list" class="mt-3"></div>
                        
                        <button id="process-btn" class="btn btn-primary btn-lg w-100 mt-3" disabled>
                            <i class="fas fa-cogs me-2"></i>
                            Start Conversion
                        </button>
                    </div>
                </div>
            </div>

            <!-- Processing Status Section -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            Processing Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-4">
                                <div class="text-center">
                                    <i class="fas fa-folder-open fa-2x text-primary mb-2"></i>
                                    <h6>Document Organization</h6>
                                    <span id="organizer-status" class="processor-status ready">Ready</span>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <i class="fas fa-search fa-2x text-success mb-2"></i>
                                    <h6>Text Extraction</h6>
                                    <span id="extractor-status" class="processor-status ready">Ready</span>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <i class="fas fa-eye fa-2x text-danger mb-2"></i>
                                    <h6>OCR Processing</h6>
                                    <span id="ocr-status" class="processor-status ready">Ready</span>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <i class="fas fa-file-code fa-2x text-info mb-2"></i>
                                    <h6>JSON Generation</h6>
                                    <span id="generator-status" class="processor-status ready">Ready</span>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <i class="fas fa-check-circle fa-2x text-warning mb-2"></i>
                                    <h6>Validation</h6>
                                    <span id="validator-status" class="processor-status ready">Ready</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Progress Bar -->
                        <div id="progress-container" class="progress-container mt-4">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Processing Progress</span>
                                <span id="progress-text">0%</span>
                            </div>
                            <div class="progress">
                                <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processing Logs Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            Processing Logs
                        </h5>
                        <button id="clear-logs" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-trash me-1"></i>
                            Clear
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div id="agent-logs" class="processing-logs"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="results-container" class="results-container mt-4">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-file-code me-2"></i>
                                JSON Preview
                            </h5>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary me-2" id="toggle-json-view">
                                    <i class="fas fa-code me-1"></i>
                                    Toggle Format
                                </button>
                                <button class="btn btn-sm btn-outline-primary me-2" id="copy-json">
                                    <i class="fas fa-copy me-1"></i>
                                    Copy
                                </button>
                                <button class="btn btn-sm btn-outline-info" id="view-json">
                                    <i class="fas fa-eye me-1"></i>
                                    View JSON
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="json-preview" class="json-preview"></div>
                        </div>
                    </div>
                    
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                Pattern Analysis Results
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="pattern-results"></div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-download me-2"></i>
                                Generated Files
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="json-files">
                                <div class="text-muted">Loading JSON files...</div>
                            </div>

                            <!-- Add refresh button -->
                            <div class="mt-3">
                                <button class="btn btn-sm btn-outline-primary" id="refresh-json-files">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    Refresh List
                                </button>
                            </div>

                            <script>
                                // Function to load and display JSON files
                                function loadJsonFiles() {
                                    const jsonFilesContainer = document.getElementById('json-files');
                                    jsonFilesContainer.innerHTML = '<div class="text-muted">Loading JSON files...</div>';

                                    fetch('/api/list-json-files')
                                        .then(response => response.json())
                                        .then(data => {
                                            if (data.success && data.json_files.length > 0) {
                                                let html = '';
                                                data.json_files.forEach(file => {
                                                    const fileSize = (file.size / 1024).toFixed(1) + ' KB';
                                                    const modifiedDate = new Date(file.modified).toLocaleDateString();
                                                    html += `
                                                        <div class="file-item mb-2 p-2 border rounded">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <div>
                                                                    <div class="file-name fw-bold">${file.name}</div>
                                                                    <small class="text-muted">${fileSize} • ${modifiedDate}</small>
                                                                </div>
                                                                <div>
                                                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="viewJsonFile('${file.path}')">
                                                                        <i class="fas fa-eye"></i>
                                                                    </button>
                                                                    <a href="/api/download/${encodeURIComponent(file.path)}" class="btn btn-sm btn-outline-success" download>
                                                                        <i class="fas fa-download"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    `;
                                                });
                                                jsonFilesContainer.innerHTML = html;
                                            } else {
                                                jsonFilesContainer.innerHTML = '<div class="text-muted">No JSON files found. Process some documents to generate JSON templates.</div>';
                                            }
                                        })
                                        .catch(error => {
                                            console.error('Error loading JSON files:', error);
                                            jsonFilesContainer.innerHTML = '<div class="text-danger">Error loading JSON files.</div>';
                                        });
                                }

                                // Function to view JSON file content
                                function viewJsonFile(filePath) {
                                    fetch(`/api/view-json/${encodeURIComponent(filePath)}`)
                                        .then(response => response.json())
                                        .then(data => {
                                            const jsonPreview = document.getElementById('json-preview');
                                            if (jsonPreview) {
                                                jsonPreview.innerHTML = '<pre><code class="language-json">' +
                                                    JSON.stringify(data, null, 2) + '</code></pre>';
                                                // Scroll to preview
                                                jsonPreview.scrollIntoView({ behavior: 'smooth' });
                                            }
                                        })
                                        .catch(error => {
                                            console.error('Error viewing JSON file:', error);
                                            alert('Error loading JSON file content.');
                                        });
                                }

                                // Load files on page load
                                document.addEventListener('DOMContentLoaded', function () {
                                    loadJsonFiles();

                                    // Add refresh button handler
                                    const refreshBtn = document.getElementById('refresh-json-files');
                                    if (refreshBtn) {
                                        refreshBtn.addEventListener('click', loadJsonFiles);
                                    }
                                });
                            </script>
                            
                            <div class="mt-4">
                                <h6 class="mb-3">Next Steps</h6>
                                <a href="/pattern-analysis" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Analyze Patterns Across Operations
                                </a>
                                <button class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-file-export me-2"></i>
                                    Export All Files as ZIP
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Use local highlight.js to avoid MIME type issues -->
    <script>
        // Create a simple syntax highlighter fallback
        window.hljs = {
            highlightElement: function(element) {
                // Simple JSON formatting without external dependencies
                if (element && element.textContent) {
                    try {
                        const json = JSON.parse(element.textContent);
                        element.innerHTML = '<pre>' + JSON.stringify(json, null, 2) + '</pre>';
                        element.style.backgroundColor = '#f8f9fa';
                        element.style.padding = '1rem';
                        element.style.borderRadius = '0.375rem';
                        element.style.border = '1px solid #dee2e6';
                    } catch (e) {
                        // If not valid JSON, just format as code
                        element.style.backgroundColor = '#f8f9fa';
                        element.style.padding = '1rem';
                        element.style.borderRadius = '0.375rem';
                        element.style.border = '1px solid #dee2e6';
                        element.style.fontFamily = 'monospace';
                    }
                }
            }
        };
    </script>
    <script src="/static/dist/vendors.db9b43404b7638d1721b.js"></script>
    <script src="/static/dist/agent.11b561b2f557ffc23bb4.js"></script>
    <script src="/socket.io/socket.io.js"></script>
    <!-- Fallback for Socket.IO if the above fails -->
    <script>
        if (typeof io === 'undefined') {
            console.warn('Loading Socket.IO from CDN as fallback');
            document.write('<script src="https://cdn.socket.io/4.7.5/socket.io.min.js"><\/script>');
        }
    </script>

    <!-- Socket.IO Configuration Script -->
    <script>
        // Configure Socket.IO with proper settings for compatibility
        window.socketConfig = {
            transports: ['polling', 'websocket'],
            upgrade: true,
            rememberUpgrade: false,
            timeout: 20000,
            forceNew: false,
            reconnection: true,
            reconnectionDelay: 1000,
            reconnectionAttempts: 5,
            maxReconnectionAttempts: 5
        };

        // Initialize socket connection with error handling
        window.initializeSocket = function() {
            if (typeof io !== 'undefined') {
                try {
                    window.socket = io(window.socketConfig);

                    window.socket.on('connect', function() {
                        console.log('Socket.IO connected successfully');
                    });

                    window.socket.on('connect_error', function(error) {
                        console.warn('Socket.IO connection error:', error);
                    });

                    window.socket.on('disconnect', function(reason) {
                        console.log('Socket.IO disconnected:', reason);
                    });

                    return window.socket;
                } catch (error) {
                    console.error('Failed to initialize Socket.IO:', error);
                    return null;
                }
            } else {
                console.error('Socket.IO library not loaded');
                return null;
            }
        };
    </script>
    <script src="/static/js/json-preview.js"></script>
    <script src="/static/js/json-display-override.js"></script>
    <script src="/static/js/ocr-status-handler.js"></script>
    
    <script>
        // Expose the agent instance globally for the override
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const agentInstance = document.querySelector('body').__vue__?.$children[0];
                if (agentInstance) {
                    window.expeditionAgent = agentInstance;
                }
            }, 100);
        });
    </script>
    
    <!-- Syntax highlighting CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/atom-one-dark.min.css">
</body>
</html>
