/**
 * Override for the JSON files display functionality
 * Enhanced with OCR status handling and Safari JSON viewing
 */

// Function to update step indicators
function updateStepIndicators(activeStep) {
    const steps = document.querySelectorAll('.step');
    if (!steps || steps.length === 0) return;
    
    steps.forEach((step, index) => {
        if (index + 1 < activeStep) {
            // Previous steps are completed
            step.classList.remove('active');
            step.classList.add('completed');
        } else if (index + 1 === activeStep) {
            // Current step is active
            step.classList.remove('completed');
            step.classList.add('active');
        } else {
            // Future steps are neither active nor completed
            step.classList.remove('active');
            step.classList.remove('completed');
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // Wait for the agent interface to initialize
    setTimeout(() => {
        // Override the displayJsonFiles function
        if (window.expeditionAgent) {
            const originalDisplayJsonFiles = window.expeditionAgent.displayJsonFiles;
            
            window.expeditionAgent.displayJsonFiles = function(jsonFiles) {
                console.log('Overriding displayJsonFiles with:', jsonFiles);
                
                const jsonFilesContainer = document.getElementById('json-files');
                if (!jsonFilesContainer) return;
                
                jsonFilesContainer.innerHTML = "<h3>Generated JSON Templates</h3>";
                
                // Check if we have the new flat structure
                if (jsonFiles && typeof jsonFiles === 'object') {
                    const fileEntries = Object.entries(jsonFiles);
                    
                    if (fileEntries.length > 0) {
                        // Create a container for all files
                        const filesContainer = document.createElement('div');
                        filesContainer.className = 'json-file-list';
                        
                        // Add each file as a download link
                        fileEntries.forEach(([fileName, filePath]) => {
                            const relativePath = makeRelativePath(filePath);
                            const fileLink = document.createElement('a');
                            fileLink.href = `/api/download/${encodeURIComponent(relativePath)}`;
                            fileLink.className = 'file-link';
                            fileLink.setAttribute('download', '');

                            // Format the display name
                            let displayName = fileName;
                            if (fileName.includes('_')) {
                                // Try to extract location and operation type
                                const parts = fileName.split('_');
                                if (parts.length >= 2) {
                                    const location = parts[0].replace(/_/g, ' ');
                                    const operationType = parts.slice(1).join(' ').replace(/_/g, ' ');
                                    displayName = `${location} - ${operationType}`;
                                }
                            }

                            fileLink.textContent = displayName;

                            // Add a view button
                            const viewButton = document.createElement('button');
                            viewButton.className = 'btn btn-sm btn-outline-info ms-2';
                            viewButton.innerHTML = '<i class="fas fa-eye"></i> View';
                            viewButton.onclick = function(e) {
                                e.preventDefault();
                                // Trigger the view in the JSON preview
                                if (window.viewJsonFile) {
                                    window.viewJsonFile(filePath);
                                } else {
                                    // Fallback to fetch and display
                                    fetch(`/api/download/${encodeURIComponent(relativePath)}?format=json`)
                                        .then(response => response.json())
                                        .then(data => {
                                            const jsonPreview = document.getElementById('json-preview');
                                            if (jsonPreview) {
                                                jsonPreview.textContent = JSON.stringify(data, null, 2);
                                                if (window.hljs) {
                                                    hljs.highlightElement(jsonPreview);
                                                }
                                            }
                                        });
                                }

                                // Show the results container if it's hidden
                                const resultsContainer = document.getElementById('results-container');
                                if (resultsContainer) {
                                    resultsContainer.style.display = 'block';
                                }

                                // Update step indicators
                                updateStepIndicators(3); // Set to step 3 (Review JSON)
                            };

                            // Add a Safari button to open in a new tab
                            const safariButton = document.createElement('button');
                            safariButton.className = 'btn btn-sm btn-outline-primary ms-2';
                            safariButton.innerHTML = '<i class="fas fa-external-link-alt"></i> Safari';
                            safariButton.onclick = function(e) {
                                e.preventDefault();
                                // Open the JSON file in a new Safari tab
                                window.open(`/api/download/${encodeURIComponent(relativePath)}?format=json`, '_blank');
                            };
                            
                            // Create a container for the file link and buttons
                            const fileLinkContainer = document.createElement('div');
                            fileLinkContainer.className = 'file-link-container mb-2 d-flex align-items-center';
                            fileLinkContainer.appendChild(fileLink);
                            fileLinkContainer.appendChild(viewButton);
                            fileLinkContainer.appendChild(safariButton);
                            
                            filesContainer.appendChild(fileLinkContainer);
                        });
                        
                        jsonFilesContainer.appendChild(filesContainer);
                    } else {
                        jsonFilesContainer.innerHTML += "<p>No JSON files were generated.</p>";
                    }
                } else if (originalDisplayJsonFiles) {
                    // Fall back to the original function if we have the old structure
                    originalDisplayJsonFiles.call(this, jsonFiles);
                }
            };
        }
    }, 500); // Wait for the agent to initialize
    
    // Utility function to convert absolute paths to relative paths for API calls
    function makeRelativePath(filePath) {
        // If it's an absolute path, convert it to relative
        if (filePath.startsWith('/')) {
            // Remove the base project path to make it relative
            const basePath = '/Users/<USER>/Desktop/Projects/File/';
            if (filePath.startsWith(basePath)) {
                return filePath.substring(basePath.length);
            }
            // If it doesn't start with our base path, try to extract just the relevant part
            const consolidatedIndex = filePath.indexOf('consolidated_outputs');
            if (consolidatedIndex !== -1) {
                return filePath.substring(consolidatedIndex);
            }
        }
        return filePath;
    }

    // Expose the viewJsonFile function globally
    window.viewJsonFile = function(filePath) {
        const relativePath = makeRelativePath(filePath);
        fetch(`/api/download/${encodeURIComponent(relativePath)}?format=json`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('JSON file fetched successfully');
                const jsonPreview = document.getElementById('json-preview');
                if (jsonPreview) {
                    const jsonString = JSON.stringify(data, null, 2);
                    jsonPreview.textContent = jsonString;
                    
                    // Apply syntax highlighting if available
                    if (window.hljs) {
                        hljs.highlightElement(jsonPreview);
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching JSON file:', error);
                const jsonPreview = document.getElementById('json-preview');
                if (jsonPreview) {
                    jsonPreview.textContent = `Error loading JSON: ${error.message}`;
                }
            });
    };
});