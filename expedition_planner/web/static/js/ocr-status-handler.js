/**
 * OCR Status Handler
 * Handles OCR status updates and UI interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the OCR status element
    const ocrStatusElement = document.getElementById('ocr-status');
    
    // Initialize socket connection if not already available
    if (!window.socket && typeof window.initializeSocket === 'function') {
        console.log('Initializing socket connection for OCR status updates');
        window.socket = window.initializeSocket();
    } else if (!window.socket && typeof io !== 'undefined') {
        console.log('Fallback socket initialization for OCR status updates');
        window.socket = io();
    } else if (!window.socket) {
        console.error('Socket.IO not available. Make sure socket.io.js is loaded.');
    } else {
        console.log('Using existing socket connection');
    }
    
    // Socket.io event handlers for OCR status
    if (window.socket) {
        // Listen for agent status updates
        window.socket.on('agent_status', function(data) {
            // Handle OCR processor status updates
            if (data.agent === 'ocr_processor') {
                if (ocrStatusElement) {
                    // Update the OCR status element
                    ocrStatusElement.textContent = data.message;
                    ocrStatusElement.className = `processor-status ${data.status}`;
                }
                
                // Log OCR status to the agent logs
                try {
                    const agentLogs = document.getElementById('agent-logs');
                    if (agentLogs) {
                        const logEntry = document.createElement('div');
                        logEntry.className = `log-entry log-${data.status === 'failed' ? 'error' : 'info'}`;
                        
                        const timestamp = new Date().toLocaleTimeString();
                        logEntry.innerHTML = `
                            <span class="log-timestamp">${timestamp}</span>
                            <span class="log-agent">OCR Processor:</span>
                            <span class="log-message">${data.message || 'Status update'}</span>
                        `;
                        
                        agentLogs.appendChild(logEntry);
                        
                        // Safely scroll to bottom
                        try {
                            agentLogs.scrollTop = agentLogs.scrollHeight;
                        } catch (scrollError) {
                            console.warn('Could not scroll logs to bottom:', scrollError);
                        }
                    }
                } catch (logError) {
                    console.error('Error updating OCR logs:', logError);
                }
                
                // Update progress based on status
                if (data.status === 'processing') {
                    updateProgress(30); // OCR is about 30% of the process
                } else if (data.status === 'completed') {
                    updateProgress(50); // OCR complete, moving to JSON generation
                }
            }
            
            // Handle text extractor status updates
            if (data.agent === 'text_extractor') {
                const extractorStatus = document.getElementById('extractor-status');
                if (extractorStatus) {
                    extractorStatus.textContent = data.message;
                    extractorStatus.className = `processor-status ${data.status}`;
                }
                
                if (data.status === 'processing') {
                    updateProgress(15); // Text extraction is about 15% of the process
                }
            }
            
            // Handle JSON generator status updates
            if (data.agent === 'json_generator') {
                const generatorStatus = document.getElementById('generator-status');
                if (generatorStatus) {
                    generatorStatus.textContent = data.message;
                    generatorStatus.className = `processor-status ${data.status}`;
                }
                
                if (data.status === 'processing') {
                    updateProgress(70); // JSON generation is about 70% of the process
                } else if (data.status === 'completed') {
                    updateProgress(90); // JSON generation complete
                }
            }
        });
        
        // Listen for processing complete event
        window.socket.on('processing_complete', function(data) {
            // Update progress to 100%
            updateProgress(100);
            
            // Update step indicators
            updateStepIndicators(3); // Move to step 3 (Review JSON)
            
            // Show extraction statistics if available
            if (data.extraction_stats) {
                try {
                    const stats = data.extraction_stats;
                    
                    // Add extraction stats to the logs
                    const agentLogs = document.getElementById('agent-logs');
                    if (agentLogs) {
                        const logEntry = document.createElement('div');
                        logEntry.className = 'log-entry log-success';
                        
                        const timestamp = new Date().toLocaleTimeString();
                        
                        // Safely access stats properties with fallbacks
                        const totalFiles = stats.total_files || 0;
                        const ocrUsed = stats.ocr_used || 0;
                        const standardExtraction = stats.standard_extraction_used || 0;
                        const combinedResults = stats.combined_results_used || 0;
                        
                        logEntry.innerHTML = `
                            <span class="log-timestamp">${timestamp}</span>
                            <span class="log-agent">Extraction Stats:</span>
                            <span class="log-message">
                                Total files: ${totalFiles}, 
                                OCR used: ${ocrUsed}, 
                                Standard extraction: ${standardExtraction}, 
                                Combined results: ${combinedResults}
                            </span>
                        `;
                        
                        agentLogs.appendChild(logEntry);
                        
                        // Safely scroll to bottom
                        try {
                            agentLogs.scrollTop = agentLogs.scrollHeight;
                        } catch (scrollError) {
                            console.warn('Could not scroll logs to bottom:', scrollError);
                        }
                    }
                } catch (statsError) {
                    console.error('Error displaying extraction stats:', statsError);
                }
            }
        });
    }
    
    // Function to update progress bar
    function updateProgress(percentage) {
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        const progressContainer = document.getElementById('progress-container');
        
        if (progressBar && progressText) {
            // Show progress container if hidden
            if (progressContainer) {
                progressContainer.style.display = 'block';
            }
            
            // Update progress bar and text
            progressBar.style.width = `${percentage}%`;
            progressText.textContent = `${percentage}%`;
            
            // Add appropriate color classes
            progressBar.className = 'progress-bar';
            if (percentage < 25) {
                progressBar.classList.add('bg-danger');
            } else if (percentage < 50) {
                progressBar.classList.add('bg-warning');
            } else if (percentage < 75) {
                progressBar.classList.add('bg-info');
            } else {
                progressBar.classList.add('bg-success');
            }
        }
    }
});