/**
 * Expedition Report Converter - Document Processing Interface
 */

import axios from 'axios';
import { io } from 'socket.io-client';
import '../css/agent-interface.scss';

class DocumentConverter {
    constructor() {
        this.socket = null;
        this.currentSession = null;
        this.agentStatus = {};
        this.processingLogs = [];
        
        this.initializeInterface();
        this.setupEventListeners();
        this.connectWebSocket();
    }
    
    initializeInterface() {
        this.elements = {
            uploadArea: document.getElementById('upload-area'),
            fileInput: document.getElementById('file-input'),
            fileList: document.getElementById('file-list'),
            processBtn: document.getElementById('process-btn'),
            expeditionName: document.getElementById('expedition-name'),
            enableAnalysis: document.getElementById('enable-analysis'),
            
            // Processor status elements
            organizerStatus: document.getElementById('organizer-status'),
            extractorStatus: document.getElementById('extractor-status'),
            generatorStatus: document.getElementById('generator-status'),
            validatorStatus: document.getElementById('validator-status'),
            
            // Progress elements
            progressContainer: document.getElementById('progress-container'),
            progressBar: document.getElementById('progress-bar'),
            progressText: document.getElementById('progress-text'),
            
            // Logs and results
            processingLogs: document.getElementById('agent-logs'),
            resultsContainer: document.getElementById('results-container'),
            
            // Pattern analysis
            patternResults: document.getElementById('pattern-results'),
            jsonFiles: document.getElementById('json-files')
        };
        
        this.updateProcessorStatus('all', 'ready');
    }
    
    setupEventListeners() {
        // File upload handling
        this.elements.uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.add('drag-over');
        });
        
        this.elements.uploadArea.addEventListener('dragleave', () => {
            this.elements.uploadArea.classList.remove('drag-over');
        });
        
        this.elements.uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.remove('drag-over');
            this.handleFileUpload(e.dataTransfer.files);
        });
        
        this.elements.uploadArea.addEventListener('click', () => {
            this.elements.fileInput.click();
        });
        
        this.elements.fileInput.addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files);
        });
        
        // Process button
        this.elements.processBtn.addEventListener('click', () => {
            this.startProcessing();
        });
        
        // Clear logs button
        document.getElementById('clear-logs')?.addEventListener('click', () => {
            this.clearLogs();
        });
    }
    
    connectWebSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            this.addLog('WebSocket connected', 'info');
        });
        
        this.socket.on('agent_status', (data) => {
            this.updateProcessorStatus(data.agent, data.status);
        });
        
        this.socket.on('processing_progress', (data) => {
            this.updateProgress(data.progress, data.message);
        });
        
        this.socket.on('agent_log', (data) => {
            this.addLog(`[${data.agent}] ${data.message}`, data.level);
        });
        
        this.socket.on('processing_complete', (data) => {
            this.handleProcessingComplete(data);
        });

        this.socket.on('joined_session', (data) => {
            this.addLog(`Joined session: ${data.session_id}`, 'info');
        });

        this.socket.on('error', (error) => {
            this.addLog(`Error: ${error.message}`, 'error');
        });
    }
    
    async handleFileUpload(files) {
        const formData = new FormData();
        const fileArray = Array.from(files);
        
        // Validate files
        const validFiles = fileArray.filter(file => {
            const validExtensions = ['.pdf', '.docx', '.doc', '.txt', '.md'];
            const extension = '.' + file.name.split('.').pop().toLowerCase();
            return validExtensions.includes(extension);
        });
        
        if (validFiles.length === 0) {
            this.addLog('No valid files selected. Supported formats: PDF, DOCX, DOC, TXT, MD', 'error');
            return;
        }
        
        // Add files to form data
        validFiles.forEach(file => {
            formData.append('files', file);
        });
        
        try {
            this.addLog(`Uploading ${validFiles.length} files...`, 'info');
            
            const response = await axios.post('/api/upload-documents', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                },
                onUploadProgress: (progressEvent) => {
                    const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                    this.updateProgress(percentCompleted, 'Uploading files...');
                }
            });
            
            if (response.data.success) {
                this.currentSession = response.data.session_id;
                this.displayUploadedFiles(response.data.files);
                this.addLog(`Successfully uploaded ${validFiles.length} files`, 'success');
                this.elements.processBtn.disabled = false;

                // Join the session room for real-time updates
                if (this.socket && this.socket.connected) {
                    this.socket.emit('join_session', { session_id: this.currentSession });
                    this.addLog(`Joining session room: ${this.currentSession}`, 'info');
                }
            } else {
                this.addLog(`Upload failed: ${response.data.error}`, 'error');
            }
            
        } catch (error) {
            this.addLog(`Upload error: ${error.message}`, 'error');
        }
    }
    
    displayUploadedFiles(files) {
        this.elements.fileList.innerHTML = '';
        
        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">${this.formatFileSize(file.size)}</span>
                </div>
                <div class="file-status">
                    <span class="status-badge ready">Ready</span>
                </div>
            `;
            this.elements.fileList.appendChild(fileItem);
        });
    }
    
    async startProcessing() {
        if (!this.currentSession) {
            this.addLog('No files uploaded', 'error');
            return;
        }
        
        const expeditionName = this.elements.expeditionName.value.trim() || 'Unnamed Expedition';
        const enableAnalysis = this.elements.enableAnalysis.checked;

        try {
            this.elements.processBtn.disabled = true;
            this.elements.progressContainer.style.display = 'block';
            this.updateProgress(0, 'Starting processing...');

            this.addLog(`Starting report conversion: ${expeditionName}`, 'info');
            this.addLog(`Pattern analysis: ${enableAnalysis ? 'Enabled' : 'Disabled'}`, 'info');

            const response = await axios.post('/api/process-expedition', {
                session_id: this.currentSession,
                expedition_name: expeditionName,
                enable_analysis: enableAnalysis
            });
            
            if (response.data.success) {
                this.addLog('Processing started successfully', 'success');
                // Progress will be updated via WebSocket
            } else {
                this.addLog(`Processing failed to start: ${response.data.error}`, 'error');
                this.elements.processBtn.disabled = false;
            }
            
        } catch (error) {
            this.addLog(`Processing error: ${error.message}`, 'error');
            this.elements.processBtn.disabled = false;
        }
    }
    
    updateProcessorStatus(processor, status) {
        const statusMap = {
            'ready': { class: 'ready', text: 'Ready' },
            'processing': { class: 'processing', text: 'Processing...' },
            'completed': { class: 'completed', text: 'Completed' },
            'error': { class: 'error', text: 'Error' }
        };
        
        const statusInfo = statusMap[status] || { class: 'unknown', text: 'Unknown' };
        
        if (processor === 'all') {
            // Update all processors
            ['organizer', 'extractor', 'generator', 'validator'].forEach(processorName => {
                this.updateSingleProcessorStatus(processorName, statusInfo);
            });
        } else {
            this.updateSingleProcessorStatus(processor, statusInfo);
        }
        
        this.agentStatus[processor] = status;
    }
    
    updateSingleProcessorStatus(processor, statusInfo) {
        const element = this.elements[`${processor}Status`];
        if (element) {
            element.className = `processor-status ${statusInfo.class}`;
            element.textContent = statusInfo.text;
        }
    }
    
    updateProgress(progress, message) {
        this.elements.progressBar.style.width = `${progress}%`;
        this.elements.progressText.textContent = message;
        
        if (progress >= 100) {
            setTimeout(() => {
                this.elements.progressContainer.style.display = 'none';
            }, 2000);
        }
    }
    
    addLog(message, level = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = {
            timestamp,
            message,
            level
        };
        
        this.processingLogs.push(logEntry);
        
        const logElement = document.createElement('div');
        logElement.className = `log-entry log-${level}`;
        logElement.innerHTML = `
            <span class="log-timestamp">${timestamp}</span>
            <span class="log-message">${message}</span>
        `;
        
        this.elements.processingLogs.appendChild(logElement);
        this.elements.processingLogs.scrollTop = this.elements.processingLogs.scrollHeight;
    }
    
    clearLogs() {
        this.elements.processingLogs.innerHTML = '';
        this.processingLogs = [];
    }
    
    handleProcessingComplete(data) {
        this.addLog('Processing completed!', 'success');
        this.updateProgress(100, 'Processing complete');
        
        // Display results
        this.displayResults(data);
        
        // Re-enable process button
        this.elements.processBtn.disabled = false;
        
        // Update all processor statuses to completed
        this.updateProcessorStatus('all', 'completed');
    }
    
    displayResults(data) {
        this.elements.resultsContainer.style.display = 'block';
        
        // Display pattern analysis
        if (data.pattern_analysis) {
            this.displayPatternAnalysis(data.pattern_analysis);
        }
        
        // Display generated JSON files
        if (data.json_files) {
            this.displayJsonFiles(data.json_files);
        }
        
        // Display summary
        this.displaySummary(data.summary);
    }
    
    displayPatternAnalysis(analysis) {
        this.elements.patternResults.innerHTML = `
            <h3>Pattern Analysis Results</h3>
            <div class="analysis-content">
                <pre>${analysis.comprehensive_report || 'No analysis report available'}</pre>
            </div>
        `;
    }
    
    displayJsonFiles(jsonFiles) {
        this.elements.jsonFiles.innerHTML = '<h3>Generated JSON Templates</h3>';
        
        Object.entries(jsonFiles).forEach(([location, files]) => {
            const locationDiv = document.createElement('div');
            locationDiv.className = 'location-files';
            locationDiv.innerHTML = `
                <h4>${location}</h4>
                <div class="file-links">
                    ${Object.entries(files).map(([opType, filePath]) => `
                        <a href="/api/download/${encodeURIComponent(filePath)}" 
                           class="file-link" download>
                            ${opType.replace('_', ' ').toUpperCase()} Operations
                        </a>
                    `).join('')}
                </div>
            `;
            this.elements.jsonFiles.appendChild(locationDiv);
        });
    }
    
    displaySummary(summary) {
        const summaryElement = document.createElement('div');
        summaryElement.className = 'processing-summary';
        summaryElement.innerHTML = `
            <h3>Processing Summary</h3>
            <div class="summary-stats">
                <div class="stat">
                    <span class="stat-label">Documents Processed:</span>
                    <span class="stat-value">${summary.documents_processed || 0}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Location Groups:</span>
                    <span class="stat-value">${summary.location_groups || 0}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">JSON Files Generated:</span>
                    <span class="stat-value">${summary.json_files_generated || 0}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Processing Time:</span>
                    <span class="stat-value">${summary.processing_time || 'Unknown'}</span>
                </div>
            </div>
        `;
        
        this.elements.resultsContainer.appendChild(summaryElement);
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Initialize the agent interface when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DocumentConverter();
});
